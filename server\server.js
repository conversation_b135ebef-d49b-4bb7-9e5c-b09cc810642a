const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const multer = require('multer');
const { PythonShell } = require('python-shell');
const cookieParser = require('cookie-parser');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
app.use(cookieParser());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const timestamp = Date.now();
    const originalName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
    cb(null, `${timestamp}-${originalName}`);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 * 1024 // 5GB limit
  }
});

// Basic test endpoint
app.get('/api/test', (req, res) => {
  res.json({ 
    status: 'success', 
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// List files endpoint
app.get('/api/list-files', (req, res) => {
  try {
    const requestedPath = req.query.path;
    if (!requestedPath) {
      return res.status(400).json({ 
        status: 'error', 
        error: 'Path parameter is required' 
      });
    }

    // Resolve the path relative to the project root
    let fullPath;
    if (path.isAbsolute(requestedPath)) {
      fullPath = requestedPath;
    } else {
      fullPath = path.join(__dirname, '..', requestedPath);
    }

    // Security check - ensure path is within project directory
    const projectRoot = path.resolve(__dirname, '..');
    const resolvedPath = path.resolve(fullPath);
    
    if (!resolvedPath.startsWith(projectRoot)) {
      return res.status(403).json({ 
        status: 'error', 
        error: 'Access denied - path outside project directory' 
      });
    }

    if (!fs.existsSync(resolvedPath)) {
      return res.status(404).json({ 
        status: 'error', 
        error: 'Directory not found' 
      });
    }

    const files = fs.readdirSync(resolvedPath);
    const fileDetails = files.map(file => {
      const filePath = path.join(resolvedPath, file);
      const stats = fs.statSync(filePath);
      
      return {
        name: file,
        path: filePath,
        size: stats.size,
        modified: stats.mtime.toISOString(),
        isDirectory: stats.isDirectory()
      };
    });

    res.json({
      status: 'success',
      path: resolvedPath,
      fileDetails: fileDetails
    });

  } catch (error) {
    console.error('Error listing files:', error);
    res.status(500).json({ 
      status: 'error', 
      error: 'Failed to list files: ' + error.message 
    });
  }
});

// File upload and processing endpoint
app.post('/api/process-audio', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        status: 'error', 
        error: 'No file uploaded' 
      });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;

    console.log('Processing file:', fileName);
    console.log('File path:', filePath);

    // Set up Server-Sent Events for real-time progress
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': 'http://localhost:3000',
      'Access-Control-Allow-Credentials': 'true'
    });

    const jobId = Date.now().toString();
    
    // Send initial connection message
    res.write(`data: ${JSON.stringify({ 
      type: 'connected', 
      jobId: jobId,
      message: 'Processing started'
    })}\n\n`);

    // Process the file with Python
    const pythonScriptPath = path.join(__dirname, '../python/direct_process.py');
    
    if (!fs.existsSync(pythonScriptPath)) {
      res.write(`data: ${JSON.stringify({ 
        type: 'error', 
        error: 'Python processing script not found' 
      })}\n\n`);
      res.end();
      return;
    }

    const options = {
      mode: 'text',
      pythonPath: 'python',
      scriptPath: path.dirname(pythonScriptPath),
      args: [filePath]
    };

    res.write(`data: ${JSON.stringify({ 
      type: 'progress', 
      stage: 'Starting Python processing...',
      progress: 10
    })}\n\n`);

    const pyshell = new PythonShell('direct_process.py', options);

    pyshell.on('message', function (message) {
      console.log('Python output:', message);
      res.write(`data: ${JSON.stringify({ 
        type: 'progress', 
        message: message,
        progress: 50
      })}\n\n`);
    });

    pyshell.end(function (err, code, signal) {
      if (err) {
        console.error('Python script error:', err);
        res.write(`data: ${JSON.stringify({ 
          type: 'error', 
          error: 'Processing failed: ' + err.message 
        })}\n\n`);
      } else {
        console.log('Python script finished with code:', code);
        res.write(`data: ${JSON.stringify({ 
          type: 'complete', 
          message: 'Processing completed successfully',
          progress: 100,
          fileName: fileName
        })}\n\n`);
      }
      res.end();
    });

  } catch (error) {
    console.error('Error processing audio:', error);
    res.write(`data: ${JSON.stringify({ 
      type: 'error', 
      error: 'Server error: ' + error.message 
    })}\n\n`);
    res.end();
  }
});

// Text file processing endpoint
app.post('/api/process-text', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ 
        status: 'error', 
        error: 'No file uploaded' 
      });
    }

    const filePath = req.file.path;
    const fileName = req.file.filename;

    console.log('Processing text file:', fileName);

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': 'http://localhost:3000',
      'Access-Control-Allow-Credentials': 'true'
    });

    const jobId = Date.now().toString();
    
    res.write(`data: ${JSON.stringify({ 
      type: 'connected', 
      jobId: jobId,
      message: 'Text processing started'
    })}\n\n`);

    // Process text file with Python
    const pythonScriptPath = path.join(__dirname, '../python/process_text_file.py');
    
    if (!fs.existsSync(pythonScriptPath)) {
      res.write(`data: ${JSON.stringify({ 
        type: 'error', 
        error: 'Text processing script not found' 
      })}\n\n`);
      res.end();
      return;
    }

    const options = {
      mode: 'text',
      pythonPath: 'python',
      scriptPath: path.dirname(pythonScriptPath),
      args: [filePath]
    };

    const pyshell = new PythonShell('process_text_file.py', options);

    pyshell.on('message', function (message) {
      console.log('Python output:', message);
      res.write(`data: ${JSON.stringify({ 
        type: 'progress', 
        message: message,
        progress: 50
      })}\n\n`);
    });

    pyshell.end(function (err, code, signal) {
      if (err) {
        console.error('Text processing error:', err);
        res.write(`data: ${JSON.stringify({ 
          type: 'error', 
          error: 'Text processing failed: ' + err.message 
        })}\n\n`);
      } else {
        res.write(`data: ${JSON.stringify({ 
          type: 'complete', 
          message: 'Text processing completed successfully',
          progress: 100,
          fileName: fileName
        })}\n\n`);
      }
      res.end();
    });

  } catch (error) {
    console.error('Error processing text:', error);
    res.write(`data: ${JSON.stringify({ 
      type: 'error', 
      error: 'Server error: ' + error.message 
    })}\n\n`);
    res.end();
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({ 
    status: 'error', 
    error: 'Internal server error' 
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Test the server at: http://localhost:${PORT}/api/test`);
});

module.exports = app;
